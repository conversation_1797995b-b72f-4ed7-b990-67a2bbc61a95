package main

import (
	"fmt"
	"gocv.io/x/gocv"
	"image"
	"image/color"
	"log"
	"os"
	"path/filepath"
	"syscall"
	"time"
	"unicode/utf16"
	"unsafe"
)

// 结构体用于调用Windows GetLastInputInfo API
type lastInputInfo struct {
	cbSize uint32
	dwTime uint32
}

// 获取系统空闲时间
func getIdleTime() time.Duration {
	user32 := syscall.NewLazyDLL("user32.dll")
	getLastInputInfo := user32.NewProc("GetLastInputInfo")

	var info lastInputInfo
	info.cbSize = uint32(unsafe.Sizeof(info))
	_, _, err := getLastInputInfo.Call(uintptr(unsafe.Pointer(&info)))
	if err != nil {
		return 0
	}

	// 当前系统启动时间（毫秒）
	tickCount := uint32(time.Now().UnixNano() / 1e6)
	idleMs := tickCount - info.dwTime

	return time.Duration(idleMs) * time.Millisecond
}

// 监控函数，最长运行10分钟，用户操作则立即停止
func startMonitoring(knownFaces []gocv.Mat, classifier gocv.CascadeClassifier, logger *log.Logger) {
	logger.Println("💤 进入空闲状态，启动人脸监控")
	webcam, err := gocv.OpenVideoCapture(0)
	if err != nil {
		logger.Println("❌ 无法打开摄像头:", err)
		return
	}
	defer func(webcam *gocv.VideoCapture) {
		err := webcam.Close()
		if err != nil {
			logger.Println("❌ 无法关闭摄像头:", err)
		}
	}(webcam)

	img := gocv.NewMat()
	defer func(img *gocv.Mat) {
		err := img.Close()
		if err != nil {
			logger.Println("❌ 无法释放图像资源:", err)
		}
	}(&img)

	startTime := time.Now()
	noFaceStartTime := time.Time{} // 记录开始没有检测到人脸的时间
	consecutiveNoFaceFrames := 0   // 连续无人脸帧数
	const maxNoFaceFrames = 100    // 最大连续无人脸帧数 (约10秒)
	hasLoggedNoFace := false       // 避免重复日志

	for {
		// 超时10分钟退出
		if time.Since(startTime) > 10*time.Minute {
			logger.Println("⏰ 监控超时10分钟，自动退出")
			break
		}

		// 用户恢复操作则退出监控
		if getIdleTime() < 10*time.Second {
			logger.Println("🛑 用户恢复操作，停止监控")
			break
		}

		if ok := webcam.Read(&img); !ok || img.Empty() {
			continue
		}

		// 人脸检测和识别逻辑
		gray := gocv.NewMat()
		defer func(gray *gocv.Mat) {
			err := gray.Close()
			if err != nil {
				logger.Println("❌ 无法释放灰度图像资源:", err)
			}
		}(&gray)

		// 转换为灰度图像以提高检测性能
		err := gocv.CvtColor(img, &gray, gocv.ColorBGRToGray)
		if err != nil {
			return
		}

		// 使用Haar级联分类器检测人脸
		rects := classifier.DetectMultiScale(gray)

		if len(rects) > 0 {
			// 重置无人脸计数器
			consecutiveNoFaceFrames = 0
			noFaceStartTime = time.Time{}
			hasLoggedNoFace = false

			logger.Printf("🔍 检测到 %d 个人脸", len(rects))
			// 检测到人脸，进行身份识别
			isAuthorized := false

			for _, rect := range rects {
				// 提取人脸区域
				faceROI := gray.Region(rect)
				defer func(faceROI *gocv.Mat) {
					err := faceROI.Close()
					if err != nil {
						logger.Println("❌ 无法释放人脸区域资源:", err)
					}
				}(&faceROI)

				// 调整人脸区域大小以便比较（统一为100x100）
				resizedFace := gocv.NewMat()
				defer func(resizedFace *gocv.Mat) {
					err := resizedFace.Close()
					if err != nil {
						logger.Println("❌ 无法释放调整大小后的人脸区域资源:", err)
					}
				}(&resizedFace)
				err := gocv.Resize(faceROI, &resizedFace, image.Point{X: 100, Y: 100}, 0, 0, gocv.InterpolationLinear)
				if err != nil {
					return
				}

				// 与已知人脸进行比较
				for i, knownFace := range knownFaces {
					// 调整已知人脸大小
					resizedKnown := gocv.NewMat()
					defer func(resizedKnown *gocv.Mat) {
						err := resizedKnown.Close()
						if err != nil {
							logger.Println("❌ 无法释放调整大小后的已知人脸资源:", err)
						}
					}(&resizedKnown)
					err := gocv.Resize(knownFace, &resizedKnown, image.Point{X: 100, Y: 100}, 0, 0, gocv.InterpolationLinear)
					if err != nil {
						return
					}

					// 使用模板匹配进行人脸比较
					result := gocv.NewMat()
					defer func(result *gocv.Mat) {
						err := result.Close()
						if err != nil {
							logger.Println("❌ 无法释放匹配结果资源:", err)
						}
					}(&result)
					err = gocv.MatchTemplate(resizedFace, resizedKnown, &result, gocv.TmCcoeffNormed, gocv.NewMat())
					if err != nil {
						return
					}

					// 获取匹配结果
					_, maxVal, _, _ := gocv.MinMaxLoc(result)
					similarity := maxVal

					logger.Printf("🔍 与已知人脸 %d 的相似度: %.3f", i+1, similarity)

					// 相似度阈值设为0.6（可根据实际情况调整）
					if similarity > 0.1 {
						logger.Printf("✅ 识别为授权用户 (相似度: %.3f)", similarity)
						isAuthorized = true
						break
					}
				}

				if isAuthorized {
					break
				}
			}

			// 如果检测到人脸但不是授权用户
			if !isAuthorized {
				logger.Println("⚠️ 检测到未授权访问！")

				// 在图像上标记检测到的人脸
				for _, rect := range rects {
					err := gocv.Rectangle(&img, rect, color.RGBA{R: 255, G: 0, B: 0, A: 255}, 2)
					if err != nil {
						return
					}
				}

				// 保存入侵者图像
				saveImage(img)

				// 显示警告
				go showAlert("安全警告", "检测到未授权人员试图访问您的计算机！")

				logger.Println("📷 已保存入侵者图像并发出警告")
			}
		} else {
			// 没有检测到人脸的处理逻辑
			consecutiveNoFaceFrames++

			// 记录开始无人脸的时间
			if noFaceStartTime.IsZero() {
				noFaceStartTime = time.Now()
			}

			// 每50帧（约5秒）记录一次无人脸状态，避免日志过多
			if consecutiveNoFaceFrames%50 == 0 && !hasLoggedNoFace {
				duration := time.Since(noFaceStartTime).Truncate(time.Second)
				logger.Printf("👻 持续 %v 未检测到人脸 (可能无人在场或摄像头被遮挡)", duration)
				hasLoggedNoFace = true
			}

			// 如果长时间无人脸，可能存在安全风险
			if consecutiveNoFaceFrames >= maxNoFaceFrames {
				duration := time.Since(noFaceStartTime).Truncate(time.Second)
				logger.Printf("⚠️ 警告：超过 %v 未检测到人脸，可能存在以下情况：", duration)
				logger.Println("   1. 无人在电脑前（正常情况）")
				logger.Println("   2. 摄像头被故意遮挡")
				logger.Println("   3. 有人故意避开摄像头")
				logger.Println("   4. 光线太暗或摄像头故障")

				// 可选：保存当前画面作为记录
				if consecutiveNoFaceFrames == maxNoFaceFrames {
					t := time.Now().Format("20060102_150405")
					path := filepath.Join("intruders", fmt.Sprintf("no_face_%s.jpg", t))
					gocv.IMWrite(path, img)
					logger.Printf("📷 已保存无人脸画面: %s", path)
				}

				// 重置计数器，避免重复警告
				if consecutiveNoFaceFrames >= maxNoFaceFrames*2 {
					consecutiveNoFaceFrames = maxNoFaceFrames
					hasLoggedNoFace = false // 允许再次记录
				}
			}
		}

		time.Sleep(100 * time.Millisecond) // 降低CPU占用
	}

	logger.Println("🛑 退出监控函数，释放摄像头资源")
}

// 保存非本人图像
func saveImage(img gocv.Mat) {
	t := time.Now().Format("20060102_150405")
	path := filepath.Join("intruders", fmt.Sprintf("intruder_%s.jpg", t))
	gocv.IMWrite(path, img)
	fmt.Println("📷 已保存图像：", path)
}

// 弹出系统警告框（Windows 原生 MessageBox）
func showAlert(title, message string) {
	user32 := syscall.NewLazyDLL("user32.dll")
	messageBoxW := user32.NewProc("MessageBoxW")

	// 转换字符串为UTF16
	titleUTF16 := utf16.Encode([]rune(title + "\x00"))
	messageUTF16 := utf16.Encode([]rune(message + "\x00"))

	// 0 = default owner, 0x10 = MB_ICONERROR
	_, _, err := messageBoxW.Call(0,
		uintptr(unsafe.Pointer(&messageUTF16[0])),
		uintptr(unsafe.Pointer(&titleUTF16[0])),
		uintptr(0x10))
	if err != nil {
		return
	}
}

// 加载所有本人照片并提取人脸区域
func loadKnownFaces(dir string, classifier gocv.CascadeClassifier) ([]gocv.Mat, error) {
	files, err := os.ReadDir(dir)
	if err != nil {
		return nil, err
	}

	var faces []gocv.Mat
	for _, file := range files {
		if file.IsDir() {
			continue
		}

		filePath := filepath.Join(dir, file.Name())
		fmt.Printf("🔍 处理已知人脸图片: %s\n", file.Name())

		// 加载彩色图像用于人脸检测
		img := gocv.IMRead(filePath, gocv.IMReadColor)
		if img.Empty() {
			fmt.Printf("⚠️ 无法加载：%s\n", file.Name())
			continue
		}
		defer img.Close()

		// 转换为灰度图像进行人脸检测
		gray := gocv.NewMat()
		defer gray.Close()
		gocv.CvtColor(img, &gray, gocv.ColorBGRToGray)

		// 检测人脸
		rects := classifier.DetectMultiScale(gray)
		fmt.Printf("   在 %s 中检测到 %d 个人脸\n", file.Name(), len(rects))

		if len(rects) == 0 {
			fmt.Printf("⚠️ 在 %s 中未检测到人脸，跳过此文件\n", file.Name())
			continue
		}

		// 如果检测到多个人脸，选择最大的那个（通常是主要人脸）
		var largestRect image.Rectangle
		maxArea := 0
		for _, rect := range rects {
			area := rect.Dx() * rect.Dy()
			if area > maxArea {
				maxArea = area
				largestRect = rect
			}
		}

		// 提取人脸区域
		faceROI := gray.Region(largestRect)

		// 调整人脸大小为标准尺寸
		resizedFace := gocv.NewMat()
		gocv.Resize(faceROI, &resizedFace, image.Point{X: 100, Y: 100}, 0, 0, gocv.InterpolationLinear)
		faceROI.Close()

		faces = append(faces, resizedFace)
		fmt.Printf("✅ 成功提取 %s 中的人脸区域 (原始: %dx%d, 人脸: %dx%d)\n",
			file.Name(), img.Cols(), img.Rows(), largestRect.Dx(), largestRect.Dy())
	}

	return faces, nil
}

func main() {
	// 初始化日志文件
	logPath := filepath.Join(".", "faceguard.log")
	logFile, err := os.OpenFile(logPath, os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0644)
	if err != nil {
		fmt.Println("❌ 无法打开日志文件:", err)
		return
	}
	defer func(logFile *os.File) {
		err := logFile.Close()
		if err != nil {
			fmt.Println("❌ 无法关闭日志文件:", err)
		}
	}(logFile)
	logger := log.New(logFile, "", log.LstdFlags)

	logger.Println("🔰 程序启动")

	// 加载 Haar 级联分类器
	classifier := gocv.NewCascadeClassifier()
	defer func(classifier *gocv.CascadeClassifier) {
		err := classifier.Close()
		if err != nil {
			logger.Println("❌ 无法释放人脸检测分类器资源:", err)
		}
	}(&classifier)

	if !classifier.Load("haarcascade_frontalface_default.xml") {
		logger.Println("❌ 无法加载人脸检测分类器")
		fmt.Println("❌ 无法加载人脸检测分类器")
		return
	}
	logger.Println("✅ 人脸检测分类器加载成功")

	// 加载已知人脸图像
	knownFaces, err := loadKnownFaces("known_faces")
	if err != nil {
		logger.Printf("❌ 无法加载已知人脸: %v", err)
		fmt.Printf("❌ 无法加载已知人脸: %v\n", err)
		return
	}
	logger.Printf("✅ 成功加载 %d 张已知人脸图像", len(knownFaces))

	if len(knownFaces) == 0 {
		logger.Println("⚠️ 警告：未找到已知人脸图像，请在 known_faces 目录中添加授权用户的照片")
		fmt.Println("⚠️ 警告：未找到已知人脸图像，请在 known_faces 目录中添加授权用户的照片")
	}

	// 主循环，检测空闲时间，空闲≥2分钟启动监控
	for {
		idle := getIdleTime()

		if idle >= 1*time.Minute {
			logger.Printf("🕑 电脑空闲超过2分钟（%v），开始启动监控\n", idle.Truncate(time.Second))
			startMonitoring(knownFaces, classifier, logger)
		} else {
			logger.Printf("🕹️ 用户活动中，空闲时间 %v\n", idle.Truncate(time.Second))
		}

		time.Sleep(5 * time.Second)
	}
}
