package main

import (
	"fmt"
	"gocv.io/x/gocv"
	"image"
	"os"
	"path/filepath"
)

// 测试人脸提取功能
func main() {
	// 加载分类器
	classifier := gocv.NewCascadeClassifier()
	defer classifier.Close()
	
	if !classifier.Load("haarcascade_frontalface_default.xml") {
		fmt.Println("❌ 无法加载人脸检测分类器")
		return
	}
	
	// 测试单张图片的人脸提取
	testImagePath := "known_faces/1.jpg"
	
	fmt.Printf("🔍 测试图片: %s\n", testImagePath)
	
	// 加载图片
	img := gocv.IMRead(testImagePath, gocv.IMReadColor)
	if img.Empty() {
		fmt.Printf("❌ 无法加载图片: %s\n", testImagePath)
		return
	}
	defer img.Close()
	
	fmt.Printf("📏 原始图片尺寸: %dx%d\n", img.Cols(), img.Rows())
	
	// 转换为灰度
	gray := gocv.NewMat()
	defer gray.Close()
	gocv.CvtColor(img, &gray, gocv.ColorBGRToGray)
	
	// 检测人脸
	rects := classifier.DetectMultiScale(gray)
	fmt.Printf("🔍 检测到 %d 个人脸\n", len(rects))
	
	if len(rects) == 0 {
		fmt.Println("❌ 未检测到人脸")
		return
	}
	
	// 显示每个检测到的人脸信息
	for i, rect := range rects {
		fmt.Printf("人脸 %d: 位置(%d,%d) 尺寸(%dx%d) 面积:%d\n", 
			i+1, rect.Min.X, rect.Min.Y, rect.Dx(), rect.Dy(), rect.Dx()*rect.Dy())
		
		// 提取并保存人脸区域
		faceROI := gray.Region(rect)
		defer faceROI.Close()
		
		// 保存原始人脸区域
		originalFacePath := fmt.Sprintf("test_face_original_%d.jpg", i+1)
		gocv.IMWrite(originalFacePath, faceROI)
		fmt.Printf("💾 保存原始人脸区域: %s\n", originalFacePath)
		
		// 调整大小并保存
		resizedFace := gocv.NewMat()
		defer resizedFace.Close()
		gocv.Resize(faceROI, &resizedFace, image.Point{X: 100, Y: 100}, 0, 0, gocv.InterpolationLinear)
		
		resizedFacePath := fmt.Sprintf("test_face_resized_%d.jpg", i+1)
		gocv.IMWrite(resizedFacePath, resizedFace)
		fmt.Printf("💾 保存调整后人脸: %s (100x100)\n", resizedFacePath)
	}
	
	fmt.Println("✅ 测试完成！请查看生成的人脸图片文件")
}
